<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>2023总结-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="2023总结"/>
  <meta name="keywords" content="slince,story"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906141842">
  <!-- Non-blocking for light theme; only blocks when dark scheme matches -->
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906141842">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906141842" media="(prefers-color-scheme: light)">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906141842">
  <link rel="prefetch" href="/static/js/search.js?t=20250906141842">
  <script src="/static/js/translations.js?t=20250906141842" defer></script>
  <script src="/static/js/language.js?t=20250906141842" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906141842",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <picture>
    <source type="image/avif" srcset="/static/img/logo.avif">
    <img class="logo" src="/static/img/logo.jpg" alt="logo" width="40" height="40" decoding="async"/>
  </picture>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="2023总结">2023总结</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2023-12-31
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#story" class="hover-underline">story</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <p>今天是 2023 年的最后一天，过了今晚十二点，这一年就结束了。去年我也是差不多年末的时候写了一篇年终总结，今年秉承去年的习惯继续来写今年的故事。</p>

<p>该从哪里说起呢？那么就从二月份开始吧，因为我记得大概是二月中旬的时候知道了考研的成绩，知道分数的那一刻，我就知道去年的努力算是付诸东流了。知道结果的时候，其实并没有太过失落，甚至是放松了一口气，因为接下来我就可以全心去找工作了。</p>

<p>大学的时候我学习的 Java 相关的内容，由于考研的缘故，很多内容基本都忘记了，因此我想学习前端相关的知识，当时想的这个入门快，看文档加上练习很快就能堆出一个网页来。现在想想，还是我太年轻了，低估了前端方向里面的内容，也高估了自己的学习能力。</p>

<p>开始学习前端之后，我在家附近办了一个月的健身卡，当时就是上午学习，下午健身，晚上回来接着再学一会，我还挺佩服自己的，除了下雨那几天，基本每天都去健身房，虽然效果一般，哈哈哈，不过后来也是可以一口气做上二三十个俯卧撑了。</p>

<p>我快速过完的 HTML、CSS 和 JS 的基础内容之后，然后就开始框架的学习，我一开始学习的是 Vue2，紧接着我就发现事情越来越不对劲了，因为要学的东西太多了，根本学不完，光是前端框架就有 Vue 、React 还有 Angular，Vue 框架还有 Vue3 版本，我的天，怎么会有这么多，紧接着，用来发送请求的 Axios 框架，还有组件框架 Element(Vue2) 和 Element-plus(Vue3)，然后还有还有JS的 ‘哥哥’ Typescript！！</p>

<p>那几天真的很焦虑很焦虑，晚上的时候会睡不着，也不敢刷小B站，因为推送的全是前端相关的，白天吃饭的时候，老妈经常会问什么时候投简历啊，去哪里啊？我总是说还没有准备好，还有很多要学的东西，不过也只有自己知道这只是我逃避就业的一个理由罢了。</p>

<p>一边学一边哭，哎，后来的操作就是掌握上面最简单最简单的使用，深入了解是没有时间的，然后跟着视频教程做了一个 Admin 的小项目，把所学的内容串了起来。然后我就开始修改简历，准备开始找工作。</p>

<p>我以为这次应该会好很多了吧？至少！结果我又傻眼了！！</p>

<p>失联招聘！！
Boss直拒！！
前程堪忧！！
58不成！！</p>

<p>投出的简历基本全是已读不回，我去网上翻了一些帖子才发现今年真的真的真的太难了！！而且裁员现象也很严重，这算是两年的脱产考研带来的不好的后果之一吧。</p>

<p>随后我转变思路，在网上请教了一些前辈，改了改简历，算是比原来‘好看’一些了，然后分别每天下午投简历，想着这时候看到的几率比较大一些。好在我运气比较好，拿到了几个面试，第一家是北京的一个xx银行信用卡中心，去了之后才傻眼了，简直了，跟他们在Boss上写的招聘需求完全不一样。</p>

<p>去了先去填个表格，个人信息之类的，然后就开始安排面试，有点让我大开眼界，一点技术问题没问，问你家里哪的，就像是在查户口。大约二十分之后面试结束，我在外面搜了一下这家公司，这就是一个办信用卡的，网上很多人被骗了，就是拿着假的JD招聘开卡的人。</p>

<p>这算是被社会毒打的第一步？？？</p>

<p>紧接着，又过了一周，拿到一个石家庄的面试，这个面试很简单，也过了，但是他让我试岗，完事才能给我签合同，当时我还不太懂试岗是什么意思，后来在论坛搜了一下，我真的是，哎，没办法，谁让现在大环境不好，人太多了呢！</p>

<p>再后来就是现在这家公司了，怎么说呢，一开始确实是被 HR 的介绍完全唬住了，说我们公司做海外结算，作为第一家公司是个很好的选择，然后我就稀里糊涂的入职了。</p>

<p>入职之前，还是先回家，参加了一个发小的婚礼，突然有点羡慕人家，同样的年纪，他已经安家结婚了，我却刚刚开始在外漂泊，不过还是和朋友们一起玩耍了几天，婚礼进行的很顺利，大家都很开心。只不过我早早离场，因为我要回家收拾行李，准备去北京了。</p>

<p>来北京之后，先是去了酒店，也是为了省点钱，选了一家便宜点的，然后他没有窗户，隔音还很差，半夜还有人唱歌，我真的，哎。</p>

<p>第二天联系了网上提前约好的中介，挨个去看房，有一些房子很好，但是价格很贵，后来我回到酒店，来到前台买了桶泡面吃，就跟老板聊起来了，刚好老板有个认识的中介，推给我了，完事就带着我去看房，这个房老而且环境很差，可是房租要低很多，考虑再三我答应了。</p>

<p>第三天签租房合同。</p>

<p>其实关于租房的这段经历还有很多，但我不太想多说什么，经历过北漂的人就知道中介有多么恶心，一个二居室能给你分出五个屋来，甚至还有的拖欠押金不还，相信有太多、太多朋友被坑过了。</p>

<p>入职工作之后，我并没有如一开始想象的那样，我在家学到的东西在工作上基本用不到，2023年了，竟然还有JSP页面，他们的项目目录也十分的混乱，都是存放在服务器上面，找个文件竟然要在盘符当中一个一个去搜，去找。</p>

<p>这家公司虽然有很多不好的地方，但至少他给我开了一份我能够生存下去的薪水，虽然不多。</p>

<p>在这家公司呆的这几个月里，我自学完了很多内容，重新学习了Vue3，还有学习了一部分 Ts，还有用来发送请求的 Axios，还学了 Node，还有它的框架，Express 和 NestJs，
学的东西其实都是围绕前端方面，这里面的NestJs虽然是一个后端框架，但远远不到熟练使用的地方，也仅仅是写简单的CRUD。</p>

<p>2024年的学习计划：</p>
<ul>
  <li>学习 React</li>
  <li>学习 NextJs</li>
  <li>如果时间足够的话，我想重新再学习下Java的一些知识</li>
</ul>

<p>接下来的这一年，我希望自己能够做到的还是沉下来，耐心的学习，一点一点的去扣一些内容，不要着急，焦虑总是会有的。</p>

<p>还有一些内容值得思考，那就是现在的就业大环境，前端这个方向人确实太多太多了，Java现在已经卷成麻花了，Go会不会是一个好的方向？？对于未来怎么样，真的不得而知，所以，<strong>我接受自己的平庸，自己就是一个普通人</strong>。</p>

<p>我还想谈一谈我的女朋友，和她的认识很意外，五一假期出去玩，她坐我旁边，然后就这么神奇的认识了，其实当时没想着会发生什么，但好像真的就像是有种魔力，牵引着两个人。</p>

<p>跟她相处到现在已经有半年了，也有一些小摩擦，但是总体来说还是没有什么问题的，她这个人很善良，也很有耐心，特别喜欢看她认真学习的样子，她学习的时候特别投入，就跟个小猫似的，累了就在那伸伸懒腰，感觉好可爱。</p>

<p>希望来年我们两个也能继续好好的！</p>

<p>这一年其实过的挺平淡的，笑过也哭过。在最后的几天里，见到了一些大学时候关系就很好的朋友；有一个是快两年没见了，一起吃了饭，聊了很多，他今年背上了房债，已经开始相亲了，对此我也只是感叹时间真的过得好快，好像昨天我们还坐一块吹牛皮捣蛋，时间真的过得好快好快。</p>

<p>另一个朋友在北京上学，我们两个也是厉害，一个下午走过了四个地铁站，晚上的时候已经快三万步了，两个人就没有方向的瞎走，也不看地图就是瞎走，没想到走到了中关村，看到了微软总部，它的设计风格很喜欢，要是有一天我也能去微软就好了。中午一起吃了驴肉火烧，哈哈哈，就在人大附近的一个小店里，味道很不错，他家的重庆小面也很好吃，酸辣口味的。</p>

<p>还有很多事没有提到，这一年真的好快，自己的变化也很多，变得不再那么容易焦躁，开始考虑一些事情的后果，又或是变得有些胆怯，不再像以前想到什么就去做什么，这算是一种成长吗！</p>

<p>应该算吧，还有很多不懂的东西，还有很多不了解的事情，需要学习，需要去了解。</p>

<p>这一年有的时候自己会想，好难受，不过还是劝自己再坚持一下，不是还有很多事没有做吗？</p>

<p>最后，也算是给自己一些慰藉吧，把心态放平，随心就好，学不会的东西就多学几次，加油！</p>

<p>新年快乐，祝自己，祝她，也祝大家。</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906141842"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906141842"></script>

</body>
</html>