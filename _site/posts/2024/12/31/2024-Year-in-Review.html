<!DOCTYPE html>
<html lang="en">
<head>
  <title>2024 Year in Review-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="2024 Year in Review"/>
  <meta name="keywords" content="slince,story"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906141842">
  <!-- Non-blocking for light theme; only blocks when dark scheme matches -->
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906141842">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906141842" media="(prefers-color-scheme: light)">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906141842">
  <link rel="prefetch" href="/static/js/search.js?t=20250906141842">
  <script src="/static/js/translations.js?t=20250906141842" defer></script>
  <script src="/static/js/language.js?t=20250906141842" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906141842",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <picture>
    <source type="image/avif" srcset="/static/img/logo.avif">
    <img class="logo" src="/static/img/logo.jpg" alt="logo" width="40" height="40" decoding="async"/>
  </picture>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="zh-CN" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="2024 Year in Review">2024 Year in Review</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2024-12-31
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#story" class="hover-underline">story</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <p>Today is the last day of the year, and as usual, it’s time for my annual review.</p>

<p>Last year, after receiving my graduate school exam results, I decided to look for work. I ended up at a traditional company doing frontend development. My daily tasks mainly involved modifying JSP pages. Though the pay wasn’t much, it was relaxed. During my free time, I studied modern frontend development concepts. I’m still learning, and the more I learn, the more I realize how much technical debt I have to catch up on.</p>

<p>At the beginning of the year, I was learning React. During this period, I discovered various open-source projects, UI component libraries, and learned to use Tailwind CSS, using these tech stacks to create many small components.</p>

<p>In April, I started an open-source project called <a href="https://github.com/slince-zero/IMaker">IMaker</a> using React as the frontend framework and NEXT UI as the UI component library. It has now garnered over 200 stars on GitHub.</p>

<p>By May, IMaker had gained over 100 stars, and I started sending out resumes and interviewing at new companies. A few companies left deep impressions. One was a legal examination preparation company with a 10/10/6 work schedule. The first interviewer there taught me a lot, helping me understand that learning technology isn’t just about frameworks but about understanding what’s behind them.</p>

<p>Another was a military industry company that I really liked. I remember the interview day was extremely hot. There were just two rounds - a technical interview that wasn’t too difficult, and an HR interview the same afternoon. They asked some questions and addressed my concerns, then told me to wait for their response. That was the end of it. Later, when I tried messaging the HR person on WeChat, they didn’t respond.</p>

<p>After June, as the weather got hotter, I became more anxious. I started applying for positions in other cities, sending resumes via email to company websites and forums where seniors had left their contact information. There were some interesting experiences during this period.</p>

<p>During this time, I attended Microsoft’s AI Day in Beijing, where I met many experts and learned about current industry trends and future directions. My main goal was still to find opportunities, and I submitted resumes to some companies, but wasn’t successful. The feedback was mostly that I lacked experience - they wanted more seasoned professionals.</p>

<p>I remember applying to a company headquartered in Beijing with a branch in Qingdao that worked on operating systems and hardware. After I sent my email, the HR response left a lasting impression. They said, “As you mentioned, your resume isn’t impressive, and your work experience and educational background don’t meet our company’s average standards…” This hit me hard at the time. Previously, I might have accepted that my education and work experience were ordinary and didn’t meet their standards. But now, I think it’s okay - take it slow, and the right opportunity will come.</p>

<p>However, at that point, my mindset was completely shattered. I couldn’t focus on learning, couldn’t settle down, and didn’t know what I was doing or should be doing. I worried about my lack of practical project experience, non-standard development practices, and the many things I didn’t know. I kept wondering what to do, feeling stuck in a learning bottleneck with no clear path forward.</p>

<p>My girlfriend advised me to be patient, stay at my current company, and properly learn what I needed to learn, but I didn’t listen.</p>

<p>In July, I had a sudden idea to try overseas opportunities, but I found the options were limited, mainly because I only had a year of experience, and most positions required three, five, or even ten years. While browsing online, I found GitHub founder’s email address. Taking a chance, I wrote him an email expressing my confusion about my future and difficulty finding new opportunities. Surprisingly, he replied, suggesting I get involved in open source and become a solid contributor to some projects. He mentioned this might be a way to bypass traditional interviews abroad.</p>

<p>This gave me new inspiration. GitHub is truly a treasure trove - I later realized that you can find almost anything you want to learn there.</p>

<p>Later, I received a message from another senior developer whose project I found on GitHub. After I emailed them, they advised me not to limit myself to technology, but to think from a product perspective - to create and deploy a product from scratch, as this process teaches you many things. They encouraged me not to give up and to keep persisting.</p>

<p>I’m truly grateful to all these seniors I met through the internet!</p>

<p>Later, I received an offer from a domestic mind-mapping company in Shenzhen. After saying goodbye to my friends, I boarded a flight to Shenzhen in mid-August, thinking this was a new beginning heading in a good direction…</p>

<p>After completing various onboarding procedures, I started developing features immediately, with no time to familiarize myself with the codebase. Since my previous employer wasn’t focused on modern frontend development, some of my self-taught knowledge wasn’t sufficient, which made me nervous. Fortunately, I managed by asking senior colleagues for help and studying after returning to my rental apartment.</p>

<p>Later, due to my knowledge gaps, I had some communication issues with the remote team members (I was the only one working on-site), which caused some development delays.</p>

<p>In September, Shenzhen’s weather was incredibly humid and hot, which was truly unbearable. Add to that the flying cockroaches, which were terrifying. Combined with development issues at the new company and the fact that I didn’t know anyone in Shenzhen, I would cry alone in my rental apartment at night.</p>

<p>In October, my girlfriend came to visit me in Shenzhen, which made me very happy. I showed her around various attractions, and at that point, everything seemed worth it. However, just when I thought things were on track, the company informed me that I hadn’t passed my probation period. Their reason was that my earlier development issues had delayed other team members’ work, though I recall only having such problems in the first two weeks, with only minor code review issues afterward. But they wouldn’t let me defend myself. My last day was set for October 30th, which happened to be my birthday. Interestingly, I received a birthday JD gift card that day.</p>

<p>A colleague who sat next to me expressed regret and chatted with me, giving some learning advice before I left this company where I’d spent less than three months. After seeing off my girlfriend, I was alone in my rental apartment, falling back into self-doubt. I decided to leave Shenzhen since I had come there specifically for this company. By late November, I had returned to my hometown.</p>

<p>One thing that really bothered me was that the Shenzhen company made me sign a non-compete agreement before leaving. I didn’t understand this well - usually, these are for long-term employees, so why require it from a new hire? And there was no compensation; they just showed me the door.</p>

<p>Back home, I met with old friends who had all found their paths in life. It felt like I was back at square one. By then it was December, and besides sending out resumes, I spent my time self-studying. Though my time at the Shenzhen company was brief, it gave me a basic framework for understanding products, and now it’s just a matter of filling in the skill gaps.</p>

<p>However, the year-end hiring situation doesn’t look promising, with barely any interviews. I’m unsure about my next steps.</p>

<p>To summarize this year’s activities: working on open-source projects, joining a new company, and becoming unemployed.</p>

<p>Analyzing this year’s events reveals my biggest problem: impatience! I act without thinking things through, without considering consequences, and when things get critical, I’m left helpless with no backup plan.</p>

<p>Now I remember what my girlfriend said, but as an adult, I have to bear the consequences of my actions.</p>

<p>Now that I’ve calmed down and reflected on this year’s events, I realize I didn’t think some things through properly. I don’t know if this counts as personal growth, but this experience has taught me a lot, especially the importance of staying calm.</p>

<p>Looking back at last year’s summary, I executed most of my learning goals well, but failed to “stay calm.” I can’t continue like this this year - it’s time to seriously consider my future path.</p>

<p>Here’s my GitHub contribution graph for the year - I’ll keep working hard next year!!</p>

<p><img src="01.png" alt="01" /></p>

<p>Finally, I wish everyone can do what they love in the new year, and may my girlfriend and parents stay healthy.</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906141842"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906141842"></script>

</body>
</html>