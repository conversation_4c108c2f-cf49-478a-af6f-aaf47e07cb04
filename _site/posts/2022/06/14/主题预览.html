<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>主题预览-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="主题预览"/>
  <meta name="keywords" content="slince,Jekyll"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906141842">
  <!-- Non-blocking for light theme; only blocks when dark scheme matches -->
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906141842">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906141842" media="(prefers-color-scheme: light)">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906141842">
  <link rel="prefetch" href="/static/js/search.js?t=20250906141842">
  <script src="/static/js/translations.js?t=20250906141842" defer></script>
  <script src="/static/js/language.js?t=20250906141842" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906141842",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <picture>
    <source type="image/avif" srcset="/static/img/logo.avif">
    <img class="logo" src="/static/img/logo.jpg" alt="logo" width="40" height="40" decoding="async"/>
  </picture>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="主题预览">主题预览</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2022-06-14
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#Jekyll" class="hover-underline">Jekyll</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <h1 id="标题">标题</h1>

<h1 id="这里是-h1">这里是 h1</h1>

<h2 id="这里是-h2">这里是 h2</h2>

<h3 id="这里是-h3">这里是 h3</h3>

<h4 id="这里是-h4">这里是 h4</h4>

<h5 id="这里是-h5">这里是 h5</h5>

<h6 id="这里是-h6">这里是 h6</h6>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code># 这里是 h1
## 这里是 h2
### 这里是 h3
#### 这里是 h4
##### 这里是 h5
###### 这里是 h6
</code></pre></div></div>

<h2 id="段落">段落</h2>

<p>段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落</p>

<p>段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落二段落</p>

<h2 id="超链接">超链接</h2>

<p><a href="http://blog.tmaize.net">TMaize Blog</a></p>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>[TMaize Blog](http://blog.tmaize.net)
</code></pre></div></div>

<h2 id="引用">引用</h2>

<blockquote>
  <p>这里是引用</p>
</blockquote>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>&gt; 这里是引用
</code></pre></div></div>

<h2 id="常见字体样式">常见字体样式</h2>

<p><em>斜体</em></p>

<p><strong>粗体</strong></p>

<p><del>删除线</del></p>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>_斜体_
**粗体**
~~删除线~~
</code></pre></div></div>

<h2 id="列表">列表</h2>

<ul>
  <li>
    <p>无序列表 1-1</p>

    <p>缩进 2 空格</p>

    <ul>
      <li>缩进 2 空格</li>
      <li>缩进 2 空格</li>
    </ul>
  </li>
  <li>无序列表 1-2</li>
  <li>无序列表 1-3</li>
</ul>

<ol>
  <li>
    <p>有序列表 1-1</p>

    <p>缩进 3 空格</p>

    <ol>
      <li>缩进 3 空格</li>
      <li>缩进 3 空格</li>
    </ol>
  </li>
  <li>有序列表 1-2</li>
  <li>有序列表 1-3</li>
</ol>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>- 无序列表 1-1

  缩进 2 空格

  - 缩进 2 空格
  - 缩进 2 空格

- 无序列表 1-2
- 无序列表 1-3

1. 有序列表 1-1

   缩进 3 空格

   1. 缩进 3 空格
   2. 缩进 3 空格

2. 有序列表 1-2
3. 有序列表 1-3
</code></pre></div></div>

<h2 id="分割线">分割线</h2>

<hr />

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>---
</code></pre></div></div>

<h2 id="图片">图片</h2>

<p>muku1<img src="https://img1.imgtp.com/2022/06/19/1Pu5lv9W.jpeg" alt="11" />
miku<img src="https://kjava.com/zs/image/YcKakK" alt="" /></p>

<div class="language-md highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="p">![</span><span class="nv">line</span><span class="p">](</span><span class="sx">http://xx.com/xx.jpg</span><span class="p">)</span>
</code></pre></div></div>

<h2 id="代码行">代码行</h2>

<p>这是一段文字<code class="language-plaintext highlighter-rouge">rm -rf /*</code>这是一段文字</p>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>这是一段文字`rm -rf /*`这是一段文字
</code></pre></div></div>

<h2 id="代码块">代码块</h2>

<div class="language-javascript highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nx">blog</span><span class="p">.</span><span class="nx">encodeHtml</span> <span class="o">=</span> <span class="nf">function </span><span class="p">(</span><span class="nx">html</span><span class="p">)</span> <span class="p">{</span>
  <span class="kd">var</span> <span class="nx">o</span> <span class="o">=</span> <span class="nb">document</span><span class="p">.</span><span class="nf">createElement</span><span class="p">(</span><span class="dl">'</span><span class="s1">div</span><span class="dl">'</span><span class="p">)</span>
  <span class="nx">o</span><span class="p">.</span><span class="nx">innerText</span> <span class="o">=</span> <span class="nx">html</span>
  <span class="kd">var</span> <span class="nx">temp</span> <span class="o">=</span> <span class="nx">o</span><span class="p">.</span><span class="nx">innerHTML</span>
  <span class="nx">o</span> <span class="o">=</span> <span class="kc">null</span>
  <span class="k">return</span> <span class="nx">temp</span>
<span class="p">}</span>
</code></pre></div></div>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code>```javascript
blog.encodeHtml = function(html) {
var o = document.createElement('div')
o.innerText = html
var temp = o.innerHTML
o = null
return temp
}
```
</code></pre></div></div>

<h2 id="表格测试">表格测试</h2>

<table>
  <thead>
    <tr>
      <th>Tables</th>
      <th style="text-align: center">Are</th>
      <th style="text-align: right">Cool</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>col 3 is</td>
      <td style="text-align: center">right-aligned</td>
      <td style="text-align: right">$1600</td>
    </tr>
    <tr>
      <td>col 2 is</td>
      <td style="text-align: center">centered</td>
      <td style="text-align: right">$12</td>
    </tr>
    <tr>
      <td>zebra stripes</td>
      <td style="text-align: center">are neat</td>
      <td style="text-align: right">$1</td>
    </tr>
  </tbody>
</table>

<div class="language-md highlighter-rouge"><div class="highlight"><pre class="highlight"><code>| Tables        |      Are      |   Cool |
| ------------- | :-----------: | -----: |
| col 3 is      | right-aligned | <span class="se">\$</span>1600 |
| col 2 is      |   centered    |   <span class="se">\$</span>12 |
| zebra stripes |   are neat    |    <span class="se">\$</span>1 |
</code></pre></div></div>

<h2 id="数学公式">数学公式</h2>

<p>需要在配置中设置<code class="language-plaintext highlighter-rouge">extMath: true</code>开启</p>

<div class="language-plaintext highlighter-rouge"><div class="highlight"><pre class="highlight"><code># 行内
\( \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} \)
$ \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} $

# 段落
\[ \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} \]
$$ \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} $$
</code></pre></div></div>

<p>Lorem ipsum dolor sit <code class="language-plaintext highlighter-rouge">\( \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} \)</code> amet consectetur adipisicing elit.</p>

<p>Lorem ipsum dolor sit <code class="language-plaintext highlighter-rouge">$ \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} $</code> amet consectetur adipisicing elit.</p>

<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Ad aut assumenda distinctio eveniet quos, saepe non quasi minus facere iste odit! Accusamus eos optio, a recusandae neque aliquam provident illum?</p>

<p><code class="language-plaintext highlighter-rouge">\[ \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} \]</code></p>

<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Ad aut assumenda distinctio eveniet quos, saepe non quasi minus facere iste odit! Accusamus eos optio, a recusandae neque aliquam provident illum?</p>

<p><code class="language-plaintext highlighter-rouge">$$ \int_0^\infty \frac{x^3}{e^x-1}\,dx = \frac{\pi^4}{15} $$</code></p>

<p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Ad aut assumenda distinctio eveniet quos, saepe non quasi minus facere iste odit! Accusamus eos optio, a recusandae neque aliquam provident illum?</p>

<h2 id="插入-html">插入 html</h2>

<div id="htmldemo"></div>
<style>
#htmldemo{
    height: 30px;
    width: 30px;
    background-color: #00aa9a;
    animation-name: moveX;
    animation-duration: 1s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
    animation-direction: alternate;
    animation-fill-mode : both;
}
@keyframes moveX {
    0%{
        transform: translateX(0px);
    }
    100%{
        transform: translateX(100px);
    }
}
</style>

<div class="language-html highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nt">&lt;div</span> <span class="na">id=</span><span class="s">"htmldemo"</span><span class="nt">&gt;&lt;/div&gt;</span>
<span class="nt">&lt;style&gt;</span>
  <span class="nf">#htmldemo</span> <span class="p">{</span>
    <span class="nl">height</span><span class="p">:</span> <span class="m">30px</span><span class="p">;</span>
    <span class="nl">width</span><span class="p">:</span> <span class="m">30px</span><span class="p">;</span>
    <span class="nl">background-color</span><span class="p">:</span> <span class="m">#00aa9a</span><span class="p">;</span>
    <span class="nl">animation-name</span><span class="p">:</span> <span class="n">moveX</span><span class="p">;</span>
    <span class="nl">animation-duration</span><span class="p">:</span> <span class="m">1s</span><span class="p">;</span>
    <span class="nl">animation-timing-function</span><span class="p">:</span> <span class="n">linear</span><span class="p">;</span>
    <span class="nl">animation-iteration-count</span><span class="p">:</span> <span class="n">infinite</span><span class="p">;</span>
    <span class="nl">animation-direction</span><span class="p">:</span> <span class="n">alternate</span><span class="p">;</span>
    <span class="nl">animation-fill-mode</span><span class="p">:</span> <span class="nb">both</span><span class="p">;</span>
  <span class="p">}</span>
  <span class="k">@keyframes</span> <span class="n">moveX</span> <span class="p">{</span>
    <span class="err">0</span><span class="o">%</span> <span class="p">{</span>
      <span class="nl">transform</span><span class="p">:</span> <span class="n">translateX</span><span class="p">(</span><span class="m">0px</span><span class="p">);</span>
    <span class="p">}</span>
    <span class="err">100</span><span class="o">%</span> <span class="p">{</span>
      <span class="nl">transform</span><span class="p">:</span> <span class="n">translateX</span><span class="p">(</span><span class="m">100px</span><span class="p">);</span>
    <span class="p">}</span>
  <span class="p">}</span>
<span class="nt">&lt;/style&gt;</span>
</code></pre></div></div>

<h2 id="插入-iframe">插入 iframe</h2>

<iframe src="//music.163.com/outchain/player?type=2&amp;id=28445796&amp;auto=0&amp;height=66" frameborder="0" width="100%" height="86px"></iframe>

<div class="language-html highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c">&lt;!-- 属性什么的不要错了，最好用双引号括住 --&gt;</span>
<span class="c">&lt;!-- 网易云的iframe需要做些调整，调整如下 --&gt;</span>
<span class="nt">&lt;iframe</span> <span class="na">src=</span><span class="s">"//music.163.com/outchain/player?type=2&amp;id=28445796&amp;auto=0&amp;height=66"</span> <span class="na">frameborder=</span><span class="s">"0"</span> <span class="na">width=</span><span class="s">"100%"</span> <span class="na">height=</span><span class="s">"86px"</span><span class="nt">&gt;&lt;/iframe&gt;</span>
</code></pre></div></div>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906141842"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906141842"></script>
    <!-- MathJax数学公式支持 -->
<style>
  .has-jax {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-color: transparent !important;
    line-height: normal !important;
    word-break: normal !important;
    padding: 0 !important;
    margin: 0 !important;
  }
  .has-jax * {
    outline: 0;
  }
</style>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    showProcessingMessages: false,
    messageStyle: "none",
    tex2jax: {
      inlineMath: [ ['$','$'], ["\\(","\\)"] ],
      displayMath: [ ['$$','$$'], ["\\[","\\]"] ],
      skipTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'a']
    },
    "HTML-CSS": {
      showMathMenu: false
    }
  });
  // 父级元素添加类名，便于css控制
  MathJax.Hub.Queue(function() {
    var all = MathJax.Hub.getAllJax();
    var i = 0;
    for(i=0; i < all.length; i += 1) {
      all[i].SourceElement().parentNode.className += ' has-jax';
    }
  });
</script>
<script
  type="text/javascript"
  src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML"
></script>

  

</body>
</html>