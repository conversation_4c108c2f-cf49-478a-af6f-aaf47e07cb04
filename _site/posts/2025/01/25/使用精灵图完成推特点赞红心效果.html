<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>使用精灵图完成推特点赞红心效果-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="使用精灵图完成推特点赞红心效果"/>
  <meta name="keywords" content="slince,前端,CSS"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906141842">
  <!-- Non-blocking for light theme; only blocks when dark scheme matches -->
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906141842">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906141842" media="(prefers-color-scheme: light)">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906141842">
  <link rel="prefetch" href="/static/js/search.js?t=20250906141842">
  <script src="/static/js/translations.js?t=20250906141842" defer></script>
  <script src="/static/js/language.js?t=20250906141842" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906141842",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <picture>
    <source type="image/avif" srcset="/static/img/logo.avif">
    <img class="logo" src="/static/img/logo.jpg" alt="logo" width="40" height="40" decoding="async"/>
  </picture>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="使用精灵图完成推特点赞红心效果">使用精灵图完成推特点赞红心效果</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2025-01-25
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#前端" class="hover-underline">前端</a>
      
        <a href="/pages/categories.html#CSS" class="hover-underline">CSS</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <p>css 有一项技术叫做 <strong>sprite sheets</strong> ，中文翻译过来就是<strong>精灵图</strong>，现代前端开发中，现在已经很少还有在用了，主要原因还是在一些页面容易模糊，另外也不容易维护，不过优点就是可以制作一些动画，可以减少 http 请求等，更详细的概念可以谷歌了解。</p>

<h2 id="效果展示">效果展示</h2>

<iframe src="/demos/twitter-redHeart.html" width="100%" height="200px" frameborder="0"></iframe>

<h2 id="实现原理">实现原理</h2>

<p>主要是利用了精灵图可以借助 <code class="language-plaintext highlighter-rouge">background-size</code> 和 <code class="language-plaintext highlighter-rouge">background-position</code> 进行图片的切割，再配合 <code class="language-plaintext highlighter-rouge">@keyframes</code> 就可以实现一个动画了。</p>

<h2 id="代码实现">代码实现</h2>

<h3 id="准备一张精灵图">准备一张精灵图</h3>

<p><img src="01.png" alt="精灵图" /></p>

<h3 id="html">HTML</h3>

<p>准备一个容器</p>

<div class="language-html highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nt">&lt;div</span> <span class="na">class=</span><span class="s">"redHeart"</span><span class="nt">&gt;&lt;/div&gt;</span>
</code></pre></div></div>

<h3 id="css">CSS</h3>

<p>我们可以先看没有动画效果的，利用 <code class="language-plaintext highlighter-rouge">background-position</code> 实现切割图片</p>

<div class="language-css highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="nc">.redHeart</span> <span class="p">{</span>
  <span class="nl">background-image</span><span class="p">:</span> <span class="sx">url('../posts/2025/01/25/01.png')</span><span class="p">;</span>
  <span class="nl">width</span><span class="p">:</span> <span class="m">100px</span><span class="p">;</span>
  <span class="nl">height</span><span class="p">:</span> <span class="m">100px</span><span class="p">;</span>
  <span class="c">/* 这里设置 2900px 的缘故是因为精灵图，分割的话有 29 张小图 */</span>
  <span class="nl">background-size</span><span class="p">:</span> <span class="m">2900px</span> <span class="m">100px</span><span class="p">;</span>
  <span class="c">/* 精灵图中的每张小图宽度是 100px ，这样就相当于移动到了第六张图 */</span>
  <span class="nl">background-position</span><span class="p">:</span> <span class="m">-500px</span> <span class="m">0px</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div></div>

<p>大概是下面的效果：</p>

<p><img src="02.png" alt="效果展示" /></p>

<p>现在可以加上动画来看看</p>

<div class="language-css highlighter-rouge"><div class="highlight"><pre class="highlight"><code> <span class="nc">.redHeart</span> <span class="p">{</span>
        <span class="nl">background-image</span><span class="p">:</span> <span class="sx">url('../posts/2025/01/25/01.png')</span><span class="p">;</span>
        <span class="nl">width</span><span class="p">:</span> <span class="m">100px</span><span class="p">;</span>
        <span class="nl">height</span><span class="p">:</span> <span class="m">100px</span><span class="p">;</span>
        <span class="nl">background-size</span><span class="p">:</span> <span class="m">2900px</span> <span class="m">100px</span><span class="p">;</span>
        <span class="nl">animation</span><span class="p">:</span> <span class="n">redHeart</span> <span class="m">2s</span> <span class="n">steps</span><span class="p">(</span><span class="m">29</span><span class="p">,</span> <span class="n">jump-none</span><span class="p">)</span> <span class="n">infinite</span><span class="p">;</span>
      <span class="p">}</span>
      <span class="k">@keyframes</span> <span class="n">redHeart</span> <span class="p">{</span>
        <span class="err">0</span><span class="o">%</span> <span class="p">{</span>
          <span class="c">/* 第一张小图 */</span>
          <span class="nl">background-position</span><span class="p">:</span> <span class="m">0px</span> <span class="m">0px</span><span class="p">;</span>
        <span class="p">}</span>
        <span class="err">100</span><span class="o">%</span> <span class="p">{</span>
          <span class="c">/* 最后一张小图，负代表左滑动 */</span>
          <span class="nl">background-position</span><span class="p">:</span> <span class="m">-2800px</span> <span class="m">0px</span><span class="p">;</span>
        <span class="p">}</span>
      <span class="p">}</span>
</code></pre></div></div>
<p>重要提示：注意上面动画规则中的steps()计时函数！这是过渡精确落在帧上所必需的。可以自己在页面上修改下 steps 的值来看下效果</p>

<p>这样就可以实现上面的效果了</p>

  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906141842"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906141842"></script>

</body>
</html>