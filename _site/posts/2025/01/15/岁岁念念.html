<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>岁岁念念-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="岁岁念念"/>
  <meta name="keywords" content="slince,story"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906141842">
  <!-- Non-blocking for light theme; only blocks when dark scheme matches -->
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/post.css?t=20250906141842">
  <link rel="stylesheet" href="/static/css/code-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <link rel="stylesheet" href="/static/css/code-light.css?t=20250906141842" media="(prefers-color-scheme: light)">
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906141842">
  <link rel="prefetch" href="/static/js/search.js?t=20250906141842">
  <script src="/static/js/translations.js?t=20250906141842" defer></script>
  <script src="/static/js/language.js?t=20250906141842" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906141842",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <picture>
    <source type="image/avif" srcset="/static/img/logo.avif">
    <img class="logo" src="/static/img/logo.jpg" alt="logo" width="40" height="40" decoding="async"/>
  </picture>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-post">
  <h1 class="title" id="岁岁念念">岁岁念念</h1>
  
  <div class="subtitle">
    <span data-i18n="post.published">发布于</span> 2025-01-15
    
    <span class="post-categories">
      <span data-i18n="post.categories">分类</span>:
      
        <a href="/pages/categories.html#story" class="hover-underline">story</a>
      
    </span>
    
  </div>
  
  
  <div class="post">
    <blockquote>
  <p>现在博客已经支持中文和英文切换了，点击右上角语言切换按钮即可，24 年终总结移动到了英文模块下。</p>
</blockquote>

<p>前几天，我写了一篇年终总结发到社区里面，受到了很多前辈的鼓励和支持，再次表示非常的感谢。这两周，参加了字节豆包的一个比赛，用 AI 来写一款软件，虽然没有入选，不过也学到了很多东西，这是提交<a href="https://hcnf64yvxdr0.feishu.cn/wiki/X16qwZ6MviwtB1k855Ucc43rnSd?from=from_copylink">地址</a>，有兴趣的可以看看。</p>

<p>另外就是参加了一些面试，这几场面试给我的感觉都很好，面试官也都很专业，也从他们身上学到了很多有用和值得思考的地方。</p>

<p>印象最深的只要是两家公司，一家是用借助 AI 做一些 C 端小应用的，他们厉害的地方在于，产品集成了市面上大多数我们经常用到的工具，比如思维导图，图片编辑工具，AI 生成头像、图片等。另外一家公司做的是面向 B 端的商业应用，是面向企业用户帮助他们快速分析数据，依托于 AI 的能力来快速落地实现相应的定制化需求。</p>

<p>我想谈一谈从这两家公司的面试中学到的东西。</p>

<p>首先是第一家，面试官问了我一个问题，“你是怎么理解 react 中的 key 的”，然后我的回答是，“key 是 react 中用来优化性能的，当我们在使用列表渲染的时候，react 会根据 key 来判断是否需要重新渲染，如果 key 相同，则不会重新渲染”。乍一听，可能没什么问题，很标准的一个回答，但是面试官却指出了一个点，他说，“你知道具体是怎样做的吗？”</p>

<p>我思考了一会，说了一些原理上的东西，不过看他的表情，可能并不是他想听到的，随后他帮我做出了解释，以下是他的一些解释，我做了一定的补充。</p>

<div class="language-bash highlighter-rouge"><div class="highlight"><pre class="highlight"><code>
<span class="nb">let </span>key <span class="o">=</span> 1<span class="p">;</span>

<span class="k">function </span>App<span class="o">()</span> <span class="o">{</span>
  <span class="k">return</span> &lt;div <span class="nv">key</span><span class="o">={</span>key<span class="o">}&gt;</span>Hello&lt;/div&gt;<span class="p">;</span>
<span class="o">}</span>
</code></pre></div></div>
<p>…做了一些操作，比如</p>

<ol>
  <li>
    <p>当 key 不变时
key = 1; ====&gt; App() React 会复用现有的组件实例，不会重新创建</p>
  </li>
  <li>
    <p>当 key 改变时
key = 2; ====&gt; App() React 会先卸载旧的组件实例，再重新创建组件实例</p>
  </li>
</ol>

<p>这个机制在实践中非常重要，比如：</p>
<ol>
  <li>当我们需要完全重置一个组件的状态时，改变它的 key 是最简单的方法</li>
  <li>在列表渲染中，key 的变化会导致对应项被视为全新元素，从而触发完整的生命周期</li>
  <li>这也解释了为什么不应该使用数组索引作为 key，因为它可能导致组件状态错乱</li>
</ol>

<div class="language-jsx highlighter-rouge"><div class="highlight"><pre class="highlight"><code><span class="c1">// ✅ 最佳实践：使用后端返回的唯一 ID</span>
<span class="kd">const</span> <span class="nx">todoList</span> <span class="o">=</span> <span class="nx">todos</span><span class="p">.</span><span class="nf">map</span><span class="p">(</span><span class="nx">todo</span> <span class="o">=&gt;</span> <span class="p">(</span>
  <span class="p">&lt;</span><span class="nc">TodoItem</span> <span class="na">key</span><span class="p">=</span><span class="si">{</span><span class="nx">todo</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span> <span class="si">{</span><span class="p">...</span><span class="nx">todo</span><span class="si">}</span> <span class="p">/&gt;</span>
<span class="p">));</span>


<span class="c1">// ✅ 使用唯一的业务标识</span>
<span class="kd">const</span> <span class="nx">userList</span> <span class="o">=</span> <span class="nx">users</span><span class="p">.</span><span class="nf">map</span><span class="p">(</span><span class="nx">user</span> <span class="o">=&gt;</span> <span class="p">(</span>
  <span class="p">&lt;</span><span class="nc">UserCard</span> <span class="na">key</span><span class="p">=</span><span class="si">{</span><span class="nx">user</span><span class="p">.</span><span class="nx">email</span><span class="si">}</span> <span class="si">{</span><span class="p">...</span><span class="nx">user</span><span class="si">}</span> <span class="p">/&gt;</span>
<span class="p">));</span>

<span class="c1">// ❌ 不要使用数组索引</span>
<span class="nx">items</span><span class="p">.</span><span class="nf">map</span><span class="p">((</span><span class="nx">item</span><span class="p">,</span> <span class="nx">index</span><span class="p">)</span> <span class="o">=&gt;</span> <span class="p">(</span>
  <span class="p">&lt;</span><span class="nc">Component</span> <span class="na">key</span><span class="p">=</span><span class="si">{</span><span class="nx">index</span><span class="si">}</span> <span class="p">/&gt;</span> <span class="c1">// 可能导致渲染错误和性能问题</span>
<span class="p">));</span>

<span class="c1">// ❌ 不要使用随机值</span>
<span class="p">&lt;</span><span class="nc">Component</span> <span class="na">key</span><span class="p">=</span><span class="si">{</span><span class="nb">Math</span><span class="p">.</span><span class="nf">random</span><span class="p">()</span><span class="si">}</span> <span class="p">/&gt;</span> <span class="c1">// 每次渲染都会创建新组件</span>

<span class="c1">// ❌ 不要使用不稳定的值</span>
<span class="p">&lt;</span><span class="nc">Component</span> <span class="na">key</span><span class="p">=</span><span class="si">{</span><span class="nb">Date</span><span class="p">.</span><span class="nf">now</span><span class="p">()</span><span class="si">}</span> <span class="p">/&gt;</span> <span class="c1">// 会导致不必要的重渲染</span>
</code></pre></div></div>

<p>其实通过上面的这个简单的问题，我们就可以看到背后的事情并不是那么简单，可能有些时候，我们作为框架的使用者，但也仅仅是作为使用者，并没有深入到背后的原理，并且分析他是如何执行和实现的。</p>

<p>另外在和他交流的过程中，也对我学习上提出了一些建议，可以先看一些简单的小工具的源码，比如 redux 核心代码非常少，然后慢慢地扩展到自己能够看 react 源码，到最后实现一个 mini react。 如果能做到这样，其实才能够真正的算是入门了。</p>

<p>后面几天，我一方面在思考自己应该怎么做，怎么学习，另一方面也在看 redux 的源码，写了一遍之后，你会发现，真的是很简单，就那么几行代码就能够实现了，真的很令人吃惊。另外也感叹，redux 的作者，能有这么优雅的解决方案，而且代码简洁明了，非常非常令人佩服。</p>

<p>自己手写一遍 redux 核心原理之后，虽然我说不上对它有多么深刻的认识，但是至少我现在对它又有了一个新的理解。我能够感觉到自己，在慢慢地构建着什么。</p>

<p>再接下来谈一谈第二家公司，我对最后一面印象很深很深，请允许我用老师来称呼对方，因为对方给我的感觉，就像是一名经验丰富的老师，在给我做一些讲解。我有问到，我对自己此时此刻以及未来的方向上有些困惑，这位老师告诉我说，“你前面提到的原理很重要，当你真正弄明白一些事物背后的原理之后，你会发现其实没什么，虽然这个时代环境并不是很好，但是你也要不断去学习，追寻你真正想要的，这不管是对于你个人还是公司，都是很重要的。”</p>

<p>虽然和这位老师聊的不是很多，但是，我回忆起一些高中时候的事情，那会我的语文老师曾教导过我，她说，成为一个像颜回那样的人，不管别人怎么说，也不要改变自己的想法。现在想想，他们这些人都是始终在做自己感兴趣的事情，并且坚持下去。</p>

<p>我对自己的要求从来不是很高，但是只要是认定了一件事，那就得做下去，就像一本书，读了开头，怎么好意思不读中间就直接看到结尾了。</p>

<p>希望自己也希望各位都能在未来有所<strong>惑</strong>。</p>


  </div>
  
  <div class="comments-container">
    <!-- Utterances 评论组件 -->
<script
  src="https://utteranc.es/client.js"
  repo="slince-zero/slince-zero.github.io"
  issue-term="pathname"
  theme="github-light"
  crossorigin="anonymous"
  async></script>

<script>
  function getCurrentTheme() {
    return document.documentElement.classList.contains('dark') ? 'dark' : 'light'
  }

  const utterancesTheme = (theme) => {
    if (theme === 'dark') {
      return 'github-dark'
    }
    return 'github-light'
  }

  // 设置评论主题
  const setUtterancesTheme = () => {
    const theme = getCurrentTheme()
    const message = {
      type: 'set-theme',
      theme: utterancesTheme(theme)
    }
    const iframe = document.querySelector('.utterances-frame')
    if (iframe) {
      iframe.contentWindow.postMessage(message, 'https://utteranc.es')
    }
  }

  // 监听主题变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.attributeName === 'class') {
        setUtterancesTheme()
      }
    })
  })

  // 开始观察 html 元素的 class 变化
  observer.observe(document.documentElement, {
    attributes: true,
    attributeFilter: ['class']
  })

  // 等待 iframe 加载完成后初始化主题
  const initTheme = () => {
    const checkIframe = setInterval(() => {
      const iframe = document.querySelector('.utterances-frame')
      if (iframe) {
        clearInterval(checkIframe)
        setUtterancesTheme()
      }
    }, 300)
    // 5秒后停止检查，避免无限循环
    setTimeout(() => clearInterval(checkIframe), 5000)
  }

  // 页面加载完成后初始化
  window.addEventListener('load', initTheme)
</script>
</div>
</div>
<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906141842"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906141842"></script>

</body>
</html>