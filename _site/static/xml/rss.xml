<?xml version="1.0" encoding="utf-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>slince Blog</title>
    <link>https://slince-zero.github.io</link>
    <description>slince的个人博客</description>
    <language>zh-CN</language>
    <pubDate>Sat, 06 Sep 2025 14:18:42 +0800</pubDate>
    <lastBuildDate>Sat, 06 Sep 2025 14:18:42 +0800</lastBuildDate>
    <generator>Jekyll v4.3.4</generator>
    <atom:link href="https://slince-zero.github.io/static/xml/rss.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>泳者之心</title>
      <link>https://slince-zero.github.io/posts/2025/05/18/%E6%B3%B3%E8%80%85%E4%B9%8B%E5%BF%83.html</link>
      <description>YoungWomanandtheSea中文译名泳者之心，讲述了那个女性运动不受关注的年代，对女性充满了傲慢与偏见，女主TrudyEderle虽然活在这样的社会当中，但始终热爱着游泳运动，并成功缔造了历史上首位横渡英吉利海峡女泳者的传奇...</description>
      <pubDate>Sun, 18 May 2025 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2025/05/18/%E6%B3%B3%E8%80%85%E4%B9%8B%E5%BF%83.html</gui>
    </item>
    <item>
      <title>使用精灵图完成推特点赞红心效果</title>
      <link>https://slince-zero.github.io/posts/2025/01/25/%E4%BD%BF%E7%94%A8%E7%B2%BE%E7%81%B5%E5%9B%BE%E5%AE%8C%E6%88%90%E6%8E%A8%E7%89%B9%E7%82%B9%E8%B5%9E%E7%BA%A2%E5%BF%83%E6%95%88%E6%9E%9C.html</link>
      <description>css有一项技术叫做spritesheets，中文翻译过来就是精灵图，现代前端开发中，现在已经很少还有在用了，主要原因还是在一些页面容易模糊，另外也不容易维护，不过优点就是可以制作一些动画，可以减少http请求等，更详细的概念可以谷歌...</description>
      <pubDate>Sat, 25 Jan 2025 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2025/01/25/%E4%BD%BF%E7%94%A8%E7%B2%BE%E7%81%B5%E5%9B%BE%E5%AE%8C%E6%88%90%E6%8E%A8%E7%89%B9%E7%82%B9%E8%B5%9E%E7%BA%A2%E5%BF%83%E6%95%88%E6%9E%9C.html</gui>
    </item>
    <item>
      <title>使用 CSS 实现无限轮播效果</title>
      <link>https://slince-zero.github.io/posts/2025/01/17/%E4%BD%BF%E7%94%A8-CSS-%E5%AE%9E%E7%8E%B0%E6%97%A0%E9%99%90%E8%BD%AE%E6%92%AD%E6%95%88%E6%9E%9C.html</link>
      <description>今天和大家分享一个使用纯CSS实现的无限轮播效果。这个轮播效果不需要任何JavaScript，完全依靠CSS动画来实现，而且支持鼠标悬停暂停。效果展示实现原理实现这个效果的核心在于：创建两组完全相同的内容使用CSSanimation让...</description>
      <pubDate>Fri, 17 Jan 2025 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2025/01/17/%E4%BD%BF%E7%94%A8-CSS-%E5%AE%9E%E7%8E%B0%E6%97%A0%E9%99%90%E8%BD%AE%E6%92%AD%E6%95%88%E6%9E%9C.html</gui>
    </item>
    <item>
      <title>babel 使用</title>
      <link>https://slince-zero.github.io/posts/2025/01/16/babel-%E4%BD%BF%E7%94%A8.html</link>
      <description>什么是BabelBabel是一个JavaScript编译器,提供了JavaScript的编译过程，能够将源代码转换为目标代码。AST-&amp;gt;Transform-&amp;gt;Generate官网https://babeljs.io/查看A...</description>
      <pubDate>Thu, 16 Jan 2025 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2025/01/16/babel-%E4%BD%BF%E7%94%A8.html</gui>
    </item>
    <item>
      <title>岁岁念念</title>
      <link>https://slince-zero.github.io/posts/2025/01/15/%E5%B2%81%E5%B2%81%E5%BF%B5%E5%BF%B5.html</link>
      <description>现在博客已经支持中文和英文切换了，点击右上角语言切换按钮即可，24年终总结移动到了英文模块下。前几天，我写了一篇年终总结发到社区里面，受到了很多前辈的鼓励和支持，再次表示非常的感谢。这两周，参加了字节豆包的一个比赛，用AI来写一款软件...</description>
      <pubDate>Wed, 15 Jan 2025 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2025/01/15/%E5%B2%81%E5%B2%81%E5%BF%B5%E5%BF%B5.html</gui>
    </item>
    <item>
      <title>2024 Year in Review</title>
      <link>https://slince-zero.github.io/posts/2024/12/31/2024-Year-in-Review.html</link>
      <description>Todayisthelastdayoftheyear,andasusual,it’stimeformyannualreview.Lastyear,afterreceivingmygraduateschoolexamresults,Id...</description>
      <pubDate>Tue, 31 Dec 2024 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2024/12/31/2024-Year-in-Review.html</gui>
    </item>
    <item>
      <title>少年泪</title>
      <link>https://slince-zero.github.io/posts/2024/08/15/%E5%B0%91%E5%B9%B4%E6%B3%AA.html</link>
      <description>今天是2024年的8月15日，距离六月份已经过去了两个多月了，本来标题想取‘年中总结’的，不过现在好像也不是年中了。所以，想来想去，想不到好的标题，恰好听到了斗破苍穹动漫的插曲，《少年泪》，音乐很好听，动漫也有很多触动我的地方。动漫中...</description>
      <pubDate>Thu, 15 Aug 2024 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2024/08/15/%E5%B0%91%E5%B9%B4%E6%B3%AA.html</gui>
    </item>
    <item>
      <title>节流、防抖以及这段时间来的感受</title>
      <link>https://slince-zero.github.io/posts/2024/05/30/%E8%8A%82%E6%B5%81-%E9%98%B2%E6%8A%96%E4%BB%A5%E5%8F%8A%E8%BF%99%E6%AE%B5%E6%97%B6%E9%97%B4%E6%9D%A5%E7%9A%84%E6%84%9F%E5%8F%97.html</link>
      <description>这几天复习到了两个概念，一个是节流，一个是防抖，他们都是可以用于浏览器性能优化。不过在说明节流和防抖的概念之前，先说一下call、apply、bind这三个函数call、apply、bind这三个函数都是JS中函数的方法，是用来改变函...</description>
      <pubDate>Thu, 30 May 2024 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2024/05/30/%E8%8A%82%E6%B5%81-%E9%98%B2%E6%8A%96%E4%BB%A5%E5%8F%8A%E8%BF%99%E6%AE%B5%E6%97%B6%E9%97%B4%E6%9D%A5%E7%9A%84%E6%84%9F%E5%8F%97.html</gui>
    </item>
    <item>
      <title>Welcome to My Blog</title>
      <link>https://slince-zero.github.io/posts/2024/01/03/hello-world.html</link>
      <description>WelcomeThisisasampleblogposttodemonstratethemultilingualsupportfeature.ThisarticleisavailableinbothEnglishandChinese,...</description>
      <pubDate>Wed, 03 Jan 2024 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2024/01/03/hello-world.html</gui>
    </item>
    <item>
      <title>2023总结</title>
      <link>https://slince-zero.github.io/posts/2023/12/31/2023%E6%80%BB%E7%BB%93.html</link>
      <description>今天是2023年的最后一天，过了今晚十二点，这一年就结束了。去年我也是差不多年末的时候写了一篇年终总结，今年秉承去年的习惯继续来写今年的故事。该从哪里说起呢？那么就从二月份开始吧，因为我记得大概是二月中旬的时候知道了考研的成绩，知道分...</description>
      <pubDate>Sun, 31 Dec 2023 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2023/12/31/2023%E6%80%BB%E7%BB%93.html</gui>
    </item>
    <item>
      <title>JS高级</title>
      <link>https://slince-zero.github.io/posts/2023/08/10/JS%E9%AB%98%E7%BA%A7.html</link>
      <description>闭包closure概念：一个函数对周围状态的引用捆绑在一起，内层函数中访问到其外层函数的作用域简单理解：闭包=内层函数+外层函数的变量functionouter(){consta=1functionf(){//内层函数用到了外面的变量...</description>
      <pubDate>Thu, 10 Aug 2023 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2023/08/10/JS%E9%AB%98%E7%BA%A7.html</gui>
    </item>
    <item>
      <title>ES6相关介绍</title>
      <link>https://slince-zero.github.io/posts/2023/06/18/ECMAScript6%E7%9B%B8%E5%85%B3%E4%BB%8B%E7%BB%8D.html</link>
      <description>ECMAScript相关介绍什么是ECMAECMA（EuropeanComputerManufacturersAssociation）中文名称为欧洲计算机制造商协会，这个组织的目标是评估、开发和认可电信和计算机标准，1994年后该组织...</description>
      <pubDate>Sun, 18 Jun 2023 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2023/06/18/ECMAScript6%E7%9B%B8%E5%85%B3%E4%BB%8B%E7%BB%8D.html</gui>
    </item>
    <item>
      <title>计算机网络</title>
      <link>https://slince-zero.github.io/posts/2023/06/17/%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%BD%91%E7%BB%9C.html</link>
      <description>第一章计算机网络体系结构相关概述计算机网络已经由一种通信基础设施发展成为一种重要的信息服务基础设施1网络、互联（连）网和因特网网路（Network）由若干结点（Node）和连接这些节点的链路（Link）组成。多个网络还可以通过路由器互...</description>
      <pubDate>Sat, 17 Jun 2023 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2023/06/17/%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%BD%91%E7%BB%9C.html</gui>
    </item>
    <item>
      <title>2022总结</title>
      <link>https://slince-zero.github.io/posts/2022/12/29/2022%E6%80%BB%E7%BB%93.html</link>
      <description>距离2022年结束还有两天，回想这一年，真的是发生了太多的事情，今年真的可以用“魔幻”和“仓促”这两个词来形容。这一年所发生的所有的高兴和不开心真的也只有自己能够体会，很幸运我今年活下来了。为什么这么说呢，上个月的这个时候，疫情还十分...</description>
      <pubDate>Thu, 29 Dec 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/12/29/2022%E6%80%BB%E7%BB%93.html</gui>
    </item>
    <item>
      <title>奈飞教程</title>
      <link>https://slince-zero.github.io/posts/2022/11/28/%E5%A5%88%E9%A3%9E%E6%95%99%E7%A8%8B.html</link>
      <description>第一步购买节点通过这个网站进行注册，购买节点https://mojie.info/#/register?code=1H9faK7U也就是可以用来fq的流量。第二步下载fq软件https://ghproxy.com/https://gi...</description>
      <pubDate>Mon, 28 Nov 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/11/28/%E5%A5%88%E9%A3%9E%E6%95%99%E7%A8%8B.html</gui>
    </item>
    <item>
      <title>好久不见</title>
      <link>https://slince-zero.github.io/posts/2022/08/14/%E5%A5%BD%E4%B9%85%E4%B8%8D%E8%A7%81.html</link>
      <description>今天是很有意思的一天，我如约而至来到北京会见了两个好朋友A和B，还有A的对象C。这次地行程如果用一个词来形容的话，那就是神奇！上午的时候，我先是和好朋友B碰面，我俩相约一起去国家博物馆，有意思的地方来了，我反应了半天才看到她，感觉自己...</description>
      <pubDate>Sun, 14 Aug 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/08/14/%E5%A5%BD%E4%B9%85%E4%B8%8D%E8%A7%81.html</gui>
    </item>
    <item>
      <title>旷野寻址</title>
      <link>https://slince-zero.github.io/posts/2022/07/31/%E6%97%B7%E9%87%8E%E5%AF%BB%E5%9D%80.html</link>
      <description>这几天睡觉做了一个很奇怪的梦，以前做个梦，基本第二天就忘记了，可这次做的梦不仅仅真实，而且为什么会有种要发生的感觉呢？我不明白这种感觉，于是把这个不长的故事记录下来……当我醒来的时候，深处一片一望无际的旷野之中，周围全是生长着一种不知...</description>
      <pubDate>Sun, 31 Jul 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/07/31/%E6%97%B7%E9%87%8E%E5%AF%BB%E5%9D%80.html</gui>
    </item>
    <item>
      <title>顺序线性表思考</title>
      <link>https://slince-zero.github.io/posts/2022/07/18/%E9%A1%BA%E5%BA%8F%E7%BA%BF%E6%80%A7%E8%A1%A8%E6%80%9D%E8%80%83.html</link>
      <description>首先看顺序表的定义：一组地址连续的存储单元，顺序存储线性表中的数据单元，使得逻辑上两个相邻的元素在物理位置上也相邻。这里就有一个疑问了，数组和顺序表又有什么关系？顺序表在计算机内以数组形式保存。线性表是从逻辑角度来看待的，它除了首和尾...</description>
      <pubDate>Mon, 18 Jul 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/07/18/%E9%A1%BA%E5%BA%8F%E7%BA%BF%E6%80%A7%E8%A1%A8%E6%80%9D%E8%80%83.html</gui>
    </item>
    <item>
      <title>天空之城</title>
      <link>https://slince-zero.github.io/posts/2022/06/27/%E5%A4%A9%E7%A9%BA%E4%B9%8B%E5%9F%8E.html</link>
      <description>前几日，偶然看到「意难平」三个字，它是在一个小说推荐视频中反复出现的弹幕，我不禁感慨道，截止到现在为止，又有多少能被我认为意难平的往事呢。视频中排在首位的是一本叫做《我的26岁房客》的书，看到书名的第一眼，很难联想到它怎会跟「意难平」...</description>
      <pubDate>Mon, 27 Jun 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/06/27/%E5%A4%A9%E7%A9%BA%E4%B9%8B%E5%9F%8E.html</gui>
    </item>
    <item>
      <title>古都洛阳</title>
      <link>https://slince-zero.github.io/posts/2022/06/20/%E5%8F%A4%E9%83%BD%E6%B4%9B%E9%98%B3.html</link>
      <description>古都洛阳2022年6月14号凌晨，廊坊的行程码终于不带星星了，终于，这座城市变成低风险地区了，这也就意味着，之前计划好久洛阳的旅途可以开始了。出发前一天的傍晚6月15号上午十点多我坐上了去往洛阳的列车，时隔多年，第一次坐这么久的火车，...</description>
      <pubDate>Mon, 20 Jun 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/06/20/%E5%8F%A4%E9%83%BD%E6%B4%9B%E9%98%B3.html</gui>
    </item>
    <item>
      <title>主题预览</title>
      <link>https://slince-zero.github.io/posts/2022/06/14/%E4%B8%BB%E9%A2%98%E9%A2%84%E8%A7%88.html</link>
      <description>标题这里是h1这里是h2这里是h3这里是h4这里是h5这里是h6#这里是h1##这里是h2###这里是h3####这里是h4#####这里是h5######这里是h6段落段落一段落一段落一段落一段落一段落一段落一段落一段落一段落一段落...</description>
      <pubDate>Tue, 14 Jun 2022 00:00:00 +0800</pubDate>
      <gui>https://slince-zero.github.io/posts/2022/06/14/%E4%B8%BB%E9%A2%98%E9%A2%84%E8%A7%88.html</gui>
    </item>
  </channel>
</rss>