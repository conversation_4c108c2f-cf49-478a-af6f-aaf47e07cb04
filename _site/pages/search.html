<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>搜索-slince Blog</title>
  <meta charset="utf-8" />
  <meta http-equiv="content-language" content="zh-CN" />
  <meta name="theme-color" content="#ffffff" />
  <meta name="supported-color-schemes" content="light dark">
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="applicable-device" content="pc,mobile">
  <meta name="author" content="slince" />
  <meta name="description" content="slince的个人博客"/>
  <meta name="keywords" content="slince,Blog,Java,Html,JavaScript"/>
  <link rel="icon" href="/static/img/favicon.ico" />
  <link rel="apple-touch-icon" href="/static/img/logo.png" />
  <link rel="stylesheet" href="/static/css/common.css?t=20250906141842">
  <!-- Non-blocking for light theme; only blocks when dark scheme matches -->
  <link rel="stylesheet" href="/static/css/theme-dark.css?t=20250906141842" media="(prefers-color-scheme: dark)">
  <!-- Defer non-critical page styles to avoid render-blocking -->
  <link rel="preload" as="style" href="/static/css/page.css?t=20250906141842" onload="this.onload=null;this.rel='stylesheet'">
  <noscript><link rel="stylesheet" href="/static/css/page.css?t=20250906141842"></noscript>
  <link rel="prefetch" href="/static/xml/search.xml?t=20250906141842">
  <link rel="prefetch" href="/static/js/search.js?t=20250906141842">
  <script src="/static/js/translations.js?t=20250906141842" defer></script>
  <script src="/static/js/language.js?t=20250906141842" defer></script>
  <script>
    window.blog = {
      baseurl:"",
      buildAt:"20250906141842",
      darkTheme: false,
      setDarkTheme: function (dark) {
        this.darkTheme = Boolean(dark);
        document.documentElement.className = this.darkTheme ? 'dark': '';
        document.querySelector('meta[name=theme-color]').setAttribute('content', this.darkTheme ? '#2D2E32': '#FFFFFF');
      }
    }
    if (sessionStorage.darkTheme !== undefined) {
      blog.setDarkTheme(sessionStorage.darkTheme === 'true'); // 记忆值，单个窗口内有效
    } else {
      blog.setDarkTheme(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches); // 跟随系统
    }
    if (window.matchMedia) {
      var media = window.matchMedia('(prefers-color-scheme: dark)');
      media.addListener(function (ev) {
        blog.setDarkTheme(ev.currentTarget.matches);
        sessionStorage.removeItem('darkTheme');
      });
    }
  </script>
</head>
<body ondragstart="return false;">
<header class="header">
  <picture>
    <source type="image/avif" srcset="/static/img/logo.avif">
    <img class="logo" src="/static/img/logo.jpg" alt="logo" width="40" height="40" decoding="async"/>
  </picture>
  <nav class="menu">
    <a href="/"  class="hover-underline" data-i18n="menu.首页">首页</a>
    <a href="/pages/categories.html"  class="hover-underline" data-i18n="menu.归类">归类</a>
    <a href="/pages/search.html"  class="hover-underline" data-i18n="menu.搜索">搜索</a>
    <a href="/pages/links.html"  class="hover-underline" data-i18n="menu.友链">友链</a>
    <a href="/pages/about.html"  class="hover-underline" data-i18n="menu.关于">关于</a>
    <a href="/" rel="alternate" hreflang="en" class="hover-underline language-switch" aria-label="Toggle language">
      <span class="lang-text">中文</span>
    </a>
  </nav>
</header>
<div class="page page-search">
  <input id="search-input" type="text" placeholder="请在这里输入关键词^_^"></input>
  <h1>
    <span>搜索结果</span>
    <img src="/static/img/loading.svg">
  </h1>
  <ul class="list-search">
    <li hidden>
      <a href="/posts/2025/05/18/%E6%B3%B3%E8%80%85%E4%B9%8B%E5%BF%83.html">
        <p class="title">泳者之心</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2025/01/25/%E4%BD%BF%E7%94%A8%E7%B2%BE%E7%81%B5%E5%9B%BE%E5%AE%8C%E6%88%90%E6%8E%A8%E7%89%B9%E7%82%B9%E8%B5%9E%E7%BA%A2%E5%BF%83%E6%95%88%E6%9E%9C.html">
        <p class="title">使用精灵图完成推特点赞红心效果</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2025/01/17/%E4%BD%BF%E7%94%A8-CSS-%E5%AE%9E%E7%8E%B0%E6%97%A0%E9%99%90%E8%BD%AE%E6%92%AD%E6%95%88%E6%9E%9C.html">
        <p class="title">使用 CSS 实现无限轮播效果</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2025/01/16/babel-%E4%BD%BF%E7%94%A8.html">
        <p class="title">babel 使用</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2025/01/15/%E5%B2%81%E5%B2%81%E5%BF%B5%E5%BF%B5.html">
        <p class="title">岁岁念念</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2024/12/31/2024-Year-in-Review.html">
        <p class="title">2024 Year in Review</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2024/08/15/%E5%B0%91%E5%B9%B4%E6%B3%AA.html">
        <p class="title">少年泪</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2024/05/30/%E8%8A%82%E6%B5%81-%E9%98%B2%E6%8A%96%E4%BB%A5%E5%8F%8A%E8%BF%99%E6%AE%B5%E6%97%B6%E9%97%B4%E6%9D%A5%E7%9A%84%E6%84%9F%E5%8F%97.html">
        <p class="title">节流、防抖以及这段时间来的感受</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2024/01/03/hello-world.html">
        <p class="title">Welcome to My Blog</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2023/12/31/2023%E6%80%BB%E7%BB%93.html">
        <p class="title">2023总结</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2023/08/10/JS%E9%AB%98%E7%BA%A7.html">
        <p class="title">JS高级</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2023/06/18/ECMAScript6%E7%9B%B8%E5%85%B3%E4%BB%8B%E7%BB%8D.html">
        <p class="title">ES6相关介绍</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2023/06/17/%E8%AE%A1%E7%AE%97%E6%9C%BA%E7%BD%91%E7%BB%9C.html">
        <p class="title">计算机网络</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/12/29/2022%E6%80%BB%E7%BB%93.html">
        <p class="title">2022总结</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/11/28/%E5%A5%88%E9%A3%9E%E6%95%99%E7%A8%8B.html">
        <p class="title">奈飞教程</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/08/14/%E5%A5%BD%E4%B9%85%E4%B8%8D%E8%A7%81.html">
        <p class="title">好久不见</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/07/31/%E6%97%B7%E9%87%8E%E5%AF%BB%E5%9D%80.html">
        <p class="title">旷野寻址</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/07/18/%E9%A1%BA%E5%BA%8F%E7%BA%BF%E6%80%A7%E8%A1%A8%E6%80%9D%E8%80%83.html">
        <p class="title">顺序线性表思考</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/06/27/%E5%A4%A9%E7%A9%BA%E4%B9%8B%E5%9F%8E.html">
        <p class="title">天空之城</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/06/20/%E5%8F%A4%E9%83%BD%E6%B4%9B%E9%98%B3.html">
        <p class="title">古都洛阳</p>
        <p class="content"></p>
      </a>
    </li>
    <li hidden>
      <a href="/posts/2022/06/14/%E4%B8%BB%E9%A2%98%E9%A2%84%E8%A7%88.html">
        <p class="title">主题预览</p>
        <p class="content"></p>
      </a>
    </li>
  </ul>
</div>

<footer
  class="footer"
  style="display: flex; justify-content: center; align-items: center">
  <span></span>
  <!-- <a href="/static/xml/rss.xml">RSS订阅</a> -->
  <span>Theme By</span>
  <a href="https://github.com/TMaize/tmaize-blog">TMaize</a>
</footer>
<div id="to-top">
  <span></span>
  <span></span>
</div>
<script type="text/javascript" src="/static/js/blog.js?t=20250906141842"></script>
<script type="text/javascript" src="/static/js/search.js?t=20250906141842"></script>
</body>
</html>
